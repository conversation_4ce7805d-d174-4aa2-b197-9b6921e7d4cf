// Artifacts View
.artifacts-view {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.artifacts-container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--code-viewer-bg, #ffffff);
  border-radius: 0 0 12px 12px;
  border: 1px solid var(--awe-split-border-color, #e0e0e0);
}

.artifacts-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--awe-split-border-color);
  background-color: var(--code-viewer-header-bg);
}

.artifacts-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.artifacts-header-title {
  display: flex;
  align-items: center;
  gap: 8px;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--body-text-color);
  }
}

.artifacts-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// File Explorer
.file-explorer {
  height: 80vh;
  border-right: 1px solid var(--awe-split-border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--code-viewer-sidebar-bg);
}

.file-explorer-header {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid var(--awe-split-border-color);
  color: var(--body-text-color);
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.file-item {
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: var(--body-text-color);

  &:hover {
    background-color: var(--hover-bg-color, rgba(0, 0, 0, 0.05));
  }

  &.selected {
    background-color: var(--selected-bg-color, rgba(0, 0, 0, 0.1));
    color: var(--selected-text-color, inherit);
  }

  .file-icon {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .file-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// File Content
.file-content {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: var(--code-viewer-bg);
  height: 80vh;
}

.no-file-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--body-text-color);
  font-style: italic;
  opacity: 0.7;
}

// Content type specific styles
.markdown-content {
  padding: 16px;
  line-height: 1.6;
  color: var(--body-text-color);
}

.image-content, .svg-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  // Layout examples container styles
  .layout-examples-container {
    width: 100%;
    height: 80vh;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    background-color: transparent;
    color: var(--body-text-color, #333333);
    padding: 20px;

    .layout-examples-header {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 30px;

      h3 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
        color: var(--body-text-color, #333333);
      }

      p {
        font-size: 14px;
        color: var(--text-secondary, #6c757d);
        line-height: 1.5;
      }
    }

    .layout-examples-grid {
      display: grid;
      gap: 20px;
      padding: 15px;

      // Dynamic grid based on number of layouts
      &.layout-count-1 {
        grid-template-columns: 1fr;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &.layout-count-2 {
        grid-template-columns: repeat(2, 1fr);

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }

      &.layout-count-3 {
        grid-template-columns: repeat(3, 1fr);

        @media (max-width: 992px) {
          grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }

      &.layout-count-4 {
        grid-template-columns: repeat(2, 1fr);

        @media (min-width: 992px) {
          grid-template-columns: repeat(4, 1fr);
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }

      // Default for 5+ layouts
      &:not(.layout-count-1):not(.layout-count-2):not(.layout-count-3):not(.layout-count-4) {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }

      /* Fullscreen layout grid for the layout identified view */
      &.layout-examples-grid-fullscreen {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 80vh;
        padding: 0;
        background-color: transparent;
      }

      .layout-example-item {
        // Full width item for single layout
        &.full-width {
          grid-column: 1 / -1; // Span all columns

          .layout-example-card {
            max-width: 600px;
            margin: 0 auto;
          }
        }

        /* Fullscreen layout item for the layout identified view */
        &.layout-example-item-fullscreen {
          width: 100%;
          height: 80vh;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .layout-example-card {
          overflow: hidden;
          transition:
            transform 0.2s ease,
            box-shadow 0.2s ease;
          background-color: transparent;
          width: 100%;
          height: 100%;

          .layout-example-image {
            height: 180px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;

            /* Fullscreen layout image for the layout identified view */
            &.layout-example-image-fullscreen {
              height: 100%;
              width: 100%;

              img {
                object-fit: contain;
                max-width: 100%;
                width: auto;
                height: auto;
              }
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              transition: transform 0.3s ease;

              &:hover {
                transform: scale(1.05);
              }
            }
          }

          .layout-example-animation {
            height: 180px;
            overflow: hidden;
            background-color: transparent;
            border-radius: 8px;
            position: relative;

            /* Fullscreen layout animation for the layout identified view */
            &.layout-example-animation-fullscreen {
              height: 100%;
              width: 100%;
              border-radius: 0;
            }

            app-layout-identified-animation {
              display: block;
              width: 100%;
              height: 100%;
            }
          }

          .layout-example-title {
            padding: 10px;
            text-align: center;
            font-weight: 500;
            font-size: 16px;
            color: var(--body-text-color, #333333);
          }

          .layout-example-description {
            padding: 0 10px 15px;
            text-align: center;
            font-size: 14px;
            color: var(--text-secondary, #6c757d);
          }
        }
      }
    }
  }
}

.text-content {
  pre {
    margin: 0;
    padding: 16px;
    background-color: var(--code-bg-color, rgba(0, 0, 0, 0.05));
    border-radius: 3px;
    overflow: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: var(--body-text-color);
  }
}

.component-content {
  padding: 16px;
  overflow: auto;
  height: 100%;
}

// Design System Component Styles
.design-system-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  background-color: var(--code-viewer-content-bg, #ffffff);
  border-radius: 8px;

  // Design System Header
  .design-system-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--awe-split-border-color);

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: var(--body-text-color);
      margin: 0;
      width: 100%;
    }

    .auto-moving {
      font-size: 14px;
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;
    }
  }

  // Design Section
  .design-section {
    width: 100%;
    margin-bottom: 32px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--body-text-color);
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--awe-split-border-color);
      width: 100%;
    }

    // Coming Soon Section Styles
    &.coming-soon-section {
      position: relative;

      .coming-soon-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.85);
        backdrop-filter: blur(4px);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        transition: all 0.3s ease;

        .coming-soon-content {
          text-align: center;
          padding: 20px;

          .coming-soon-icon {
            font-size: 48px;
            margin-bottom: 12px;
            animation: bounce 2s infinite;
          }

          .coming-soon-text {
            font-size: 24px;
            font-weight: 600;
            color: var(--body-text-color, #333);
            margin-bottom: 8px;
          }

          .coming-soon-subtitle {
            font-size: 14px;
            color: var(--text-secondary, #666);
            opacity: 0.8;
          }
        }

        // Diagonal overlay for Project Overview
        &.diagonal-overlay {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
          transform: rotate(-5deg);
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

          .coming-soon-content {
            transform: rotate(5deg);
          }
        }
      }

      // Combined sections styling
      &.combined-sections {
        .coming-soon-overlay {
          .coming-soon-content {
            .coming-soon-subtitle {
              max-width: 300px;
              margin: 0 auto;
            }
          }
        }
      }
    }

    // Project Overview section styling
    &.project-overview-section {
      position: relative;
      min-height: 200px;

      // Blur the content behind the overlay
      > *:not(.coming-soon-overlay) {
        filter: blur(1px);
        opacity: 0.6;
      }
    }
  }

  // Design Section Divider
  .design-section-divider {
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--awe-split-border-color, #e0e0e0) 20%,
      var(--awe-split-border-color, #e0e0e0) 80%,
      transparent 100%
    );
    margin: 32px 0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 8px;
      background: var(--primary-color, #6566CD);
      border-radius: 50%;
      box-shadow: 0 0 0 3px var(--code-viewer-content-bg, #ffffff);
    }
  }

  // Font Styles
  .font-styles {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
    width: 100%;

    .font-style-item {
      display: flex;
      flex-direction: column;
      width: 100%;

      .headline-1 {
        font-size: 64px;
        line-height: 76px;
        font-weight: 600;
        margin: 0;
      }

      .headline-2 {
        font-size: 36px;
        line-height: 44px;
        font-weight: 600;
        margin: 0;
      }

      .headline-3 {
        font-size: 28px;
        line-height: 36px;
        font-weight: 600;
        margin: 0;
      }

      .headline-4 {
        font-size: 20px;
        line-height: 28px;
        font-weight: 600;
        margin: 0;
      }

      .font-details {
        font-size: 14px;
        color: var(--text-secondary);
        margin-top: 4px;
      }
    }
  }

  // Font Preview
  .font-preview {
    display: flex;
    justify-content: center;
    margin: 20px 0;

    img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      border: 1px solid var(--awe-split-border-color);
    }
  }

  // Color Swatches
  .color-swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    width: 100%;
    justify-content: flex-start;

    .color-swatch {
      display: flex;
      flex-direction: column;
      width: 100px;
      flex-shrink: 0;

      .color-box {
        width: 100%;
        height: 80px;
        border-radius: 8px;
        margin-bottom: 8px;
        border: 1px solid var(--awe-split-border-color);
      }

      .color-details {
        display: flex;
        flex-direction: column;

        .color-name {
          font-size: 14px;
          font-weight: 600;
          color: var(--body-text-color);
        }

        .color-value {
          font-size: 12px;
          color: var(--text-secondary);

          .token-input {
            width: 100%;
            padding: 4px;
            border: 1px solid var(--awe-split-border-color);
            border-radius: 4px;
            font-size: 12px;
            background-color: var(--code-viewer-content-bg);
            color: var(--body-text-color);
            transition: all 0.2s ease-in-out;

            &:focus {
              outline: none;
              border-color: var(--primary-color, #6566CD);
              box-shadow: 0 0 0 2px rgba(101, 102, 205, 0.2);
            }
            &.invalid {
              border-color: #f44336;
              box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
              background-color: rgba(244, 67, 54, 0.05);
            }
          }
        }
      }
    }
  }

  // Button Samples
  .button-samples {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    width: 100%;
    justify-content: flex-start;

    .button-sample {
      .sample-button {
        padding: 10px 20px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;

        &.primary {
          background-color: #E48900;
          color: white;
          border: none;
        }

        &.secondary {
          background-color: #FFA826;
          color: white;
          border: none;
        }

        &.outline {
          background-color: transparent;
          color: #212121;
          border: 1px solid #212121;
        }

        &.text {
          background-color: transparent;
          color: #212121;
          border: none;
          padding: 10px 0;
        }

        .button-icon {
          font-size: 16px;
        }
      }
    }
  }
}

// Shimmer effect for component content
.shimmer-container {
  width: 100%;

  .shimmer-block {
    height: 20px;
    margin-bottom: 12px;
    background: linear-gradient(90deg,
      var(--shimmer-bg-start, rgba(0, 0, 0, 0.05)) 0%,
      var(--shimmer-bg-middle, rgba(0, 0, 0, 0.1)) 50%,
      var(--shimmer-bg-start, rgba(0, 0, 0, 0.05)) 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;

    &:nth-child(1) {
      width: 100%;
    }

    &:nth-child(2) {
      width: 80%;
    }

    &:nth-child(3) {
      width: 60%;
    }
  }
}

// File icon classes
.file-icon-md::before {
  content: "📄";
}

.file-icon-img::before {
  content: "🖼️";
}

.file-icon-svg::before {
  content: "🔍";
}

.file-icon-txt::before {
  content: "📝";
}

.file-icon-component::before {
  content: "🧩";
}

.file-icon-default::before {
  content: "📁";
}

// Animation for shimmer effect
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Animation for coming soon icon bounce
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// Dark theme support for design system and layout examples
:host-context(.dark-theme) {
  // Layout examples container dark theme
  .layout-examples-container {
    background-color: transparent;

    .layout-examples-header {
      h3 {
        color: var(--body-text-color, #e0e0e0);
      }

      p {
        color: var(--text-secondary, #a0a0a0);
      }
    }

    .layout-example-card {
      background-color: transparent;
      border-color: var(--border-color, #333333);

      .layout-example-image {
        background-color: transparent;
      }

      .layout-example-title {
        color: var(--body-text-color, #e0e0e0);
      }

      .layout-example-description {
        color: var(--text-secondary, #a0a0a0);
      }
    }
  }

  .design-system-container {
    background-color: var(--code-viewer-content-bg, #1e1e1e);

    .design-system-header {
      border-bottom-color: var(--awe-split-border-color, #333);

      h2 {
        color: var(--body-text-color, #e0e0e0);
      }

      .auto-moving {
        color: var(--text-secondary, #a0a0a0);
      }
    }

    .design-section {
      h3 {
        color: var(--body-text-color, #e0e0e0);
        border-bottom-color: var(--awe-split-border-color, #333);
      }

      // Dark theme coming soon overlay
      &.coming-soon-section {
        .coming-soon-overlay {
          background: rgba(30, 30, 30, 0.85);
          backdrop-filter: blur(4px);

          .coming-soon-content {
            .coming-soon-text {
              color: var(--body-text-color, #e0e0e0);
            }

            .coming-soon-subtitle {
              color: var(--text-secondary, #a0a0a0);
            }
          }

          // Dark theme diagonal overlay
          &.diagonal-overlay {
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(30, 30, 30, 0.7) 100%);
          }
        }
      }
    }

    // Dark theme divider
    .design-section-divider {
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--awe-split-border-color, #333) 20%,
        var(--awe-split-border-color, #333) 80%,
        transparent 100%
      );

      &::before {
        background: var(--primary-color, #6566CD);
        box-shadow: 0 0 0 3px var(--code-viewer-content-bg, #1e1e1e);
      }
    }

    .font-styles {
      .font-style-item {
        .headline-1, .headline-2, .headline-3, .headline-4 {
          color: var(--body-text-color, #e0e0e0);
        }

        .font-details {
          color: var(--text-secondary, #a0a0a0);
        }
      }
    }

    .font-preview {
      img {
        border-color: var(--awe-split-border-color, #333);
      }
    }

    .color-swatches {
      .color-swatch {
        .color-box {
          border-color: var(--awe-split-border-color, #333);
        }

        .color-details {
          .color-name {
            color: var(--body-text-color, #e0e0e0);
          }

          .color-value {
            color: var(--text-secondary, #a0a0a0);

            .token-input {
              background-color: var(--code-viewer-content-bg, #1e1e1e);
              color: var(--body-text-color, #e0e0e0);
              border-color: var(--awe-split-border-color, #333);

              &:focus {
                border-color: var(--primary-color, #6566CD);
                box-shadow: 0 0 0 2px rgba(101, 102, 205, 0.3);
              }
            }
          }
        }
      }
    }

    .button-samples {
      .button-sample {
        .sample-button {
          &.outline {
            color: #e0e0e0;
            border-color: #e0e0e0;
          }

          &.text {
            color: #e0e0e0;
          }
        }
      }
    }
  }

  .shimmer-container {
    .shimmer-block {
      background: linear-gradient(90deg,
        var(--shimmer-bg-start, rgba(255, 255, 255, 0.05)) 0%,
        var(--shimmer-bg-middle, rgba(255, 255, 255, 0.1)) 50%,
        var(--shimmer-bg-start, rgba(255, 255, 255, 0.05)) 100%);
    }
  }
}
